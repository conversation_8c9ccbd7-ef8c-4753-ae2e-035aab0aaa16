#!/usr/bin/env python3
"""
FFmpeg 验证脚本
检查FFmpeg安装状态和功能
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🔍 {title}")
    print("=" * 60)

def check_ffmpeg_in_path():
    """检查FFmpeg是否在PATH中"""
    print_header("检查FFmpeg PATH状态")
    
    ffmpeg_path = shutil.which('ffmpeg')
    if ffmpeg_path:
        print(f"✅ FFmpeg 在PATH中找到")
        print(f"📍 路径: {ffmpeg_path}")
        return ffmpeg_path
    else:
        print("❌ FFmpeg 不在PATH中")
        return None

def check_ffmpeg_common_locations():
    """检查FFmpeg常见安装位置"""
    print_header("检查FFmpeg常见安装位置")
    
    common_paths = [
        "C:/ffmpeg/bin/ffmpeg.exe",
        "C:/ffmpeg/ffmpeg.exe", 
        "ffmpeg.exe",
        os.path.join(os.getcwd(), "ffmpeg.exe"),
    ]
    
    found_paths = []
    for path in common_paths:
        if os.path.isfile(path):
            print(f"✅ 找到: {path}")
            found_paths.append(path)
        else:
            print(f"❌ 未找到: {path}")
    
    return found_paths

def test_ffmpeg_version(ffmpeg_path):
    """测试FFmpeg版本"""
    print_header(f"测试FFmpeg功能: {ffmpeg_path}")
    
    try:
        # 测试版本命令
        result = subprocess.run(
            [ffmpeg_path, '-version'], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ 版本信息: {version_line}")
            
            # 检查编解码器支持
            print("\n🔍 检查关键编解码器支持:")
            codecs_result = subprocess.run(
                [ffmpeg_path, '-codecs'], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if 'libx264' in codecs_result.stdout:
                print("✅ H.264 编码器 (libx264) 支持")
            else:
                print("⚠️ H.264 编码器 (libx264) 不支持")
                
            if 'flv' in codecs_result.stdout:
                print("✅ FLV 格式支持")
            else:
                print("⚠️ FLV 格式不支持")
            
            return True
        else:
            print(f"❌ FFmpeg 执行失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ FFmpeg 执行超时")
        return False
    except Exception as e:
        print(f"❌ FFmpeg 测试出错: {e}")
        return False

def test_rtmp_capability(ffmpeg_path):
    """测试RTMP推流能力"""
    print_header("测试RTMP推流能力")
    
    try:
        # 测试RTMP协议支持
        result = subprocess.run(
            [ffmpeg_path, '-protocols'], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        
        if 'rtmp' in result.stdout:
            print("✅ RTMP 协议支持")
            return True
        else:
            print("❌ RTMP 协议不支持")
            return False
            
    except Exception as e:
        print(f"❌ RTMP 测试出错: {e}")
        return False

def generate_report(results):
    """生成检查报告"""
    print_header("FFmpeg 检查报告")
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    print(f"📊 测试结果: {passed_tests}/{total_tests} 通过")
    print()
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print()
    
    if passed_tests == total_tests:
        print("🎉 FFmpeg 完全可用！")
        print("💡 YOLOv8 检测系统可以正常使用RTMP流功能")
    else:
        print("⚠️ FFmpeg 存在问题")
        print()
        print("🔧 建议解决方案:")
        if not results.get("PATH检查", True):
            print("   - 运行 install_ffmpeg.bat 安装FFmpeg")
            print("   - 或手动添加FFmpeg到系统PATH")
        if not results.get("版本测试", True):
            print("   - 重新安装FFmpeg")
            print("   - 检查FFmpeg文件是否损坏")
        if not results.get("RTMP支持", True):
            print("   - 安装完整版FFmpeg (包含RTMP支持)")

def main():
    """主函数"""
    print("🔍 FFmpeg 安装验证工具")
    print("=" * 60)
    
    results = {}
    
    # 检查PATH
    ffmpeg_in_path = check_ffmpeg_in_path()
    results["PATH检查"] = ffmpeg_in_path is not None
    
    # 检查常见位置
    found_paths = check_ffmpeg_common_locations()
    
    # 选择要测试的FFmpeg路径
    test_path = ffmpeg_in_path or (found_paths[0] if found_paths else None)
    
    if test_path:
        # 测试版本
        results["版本测试"] = test_ffmpeg_version(test_path)
        
        if results["版本测试"]:
            # 测试RTMP支持
            results["RTMP支持"] = test_rtmp_capability(test_path)
        else:
            results["RTMP支持"] = False
    else:
        print_header("未找到FFmpeg")
        print("❌ 系统中未找到FFmpeg")
        print("💡 请运行 install_ffmpeg.bat 安装FFmpeg")
        results["版本测试"] = False
        results["RTMP支持"] = False
    
    # 生成报告
    generate_report(results)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
