import subprocess
import cv2
import os
import shutil


class StreamManager:
    """
    RTMP流管理器，用于管理多个不同分辨率和帧率的视频流

    属性:
        rtmp_url (str): RTMP服务器地址
        processes (dict): 存储不同配置的流处理器
        ffmpeg_path (str): FFmpeg可执行文件路径
    """

    def __init__(self, rtmp_url):
        """
        初始化流管理器

        参数:
            rtmp_url: RTMP服务器地址 (如: rtmp://localhost/live/stream)
        """
        self.rtmp_url = rtmp_url
        self.processes = {}  # 存储不同分辨率/帧率的流处理器
        self.ffmpeg_path = self._find_ffmpeg_path()  # 查找FFmpeg路径

    def _find_ffmpeg_path(self):
        """
        查找FFmpeg可执行文件路径

        返回:
            str: FFmpeg可执行文件的完整路径
        """
        # 常见的FFmpeg安装路径（Windows）
        possible_paths = [
            'C:/ffmpeg/bin/ffmpeg.exe',  # 用户指定的安装路径（最常见）
            'C:/ffmpeg/ffmpeg.exe',  # 用户指定的安装路径（简化）
            'ffmpeg',  # 系统PATH中
            'ffmpeg.exe',  # 系统PATH中（Windows）
            os.path.join(os.getcwd(), 'ffmpeg.exe'),  # 当前目录
            os.path.join(os.path.dirname(__file__), '..', '..', 'ffmpeg.exe'),  # 项目根目录
        ]

        for path in possible_paths:
            if shutil.which(path) or os.path.isfile(path):
                print(f"✅ 找到FFmpeg: {path}")
                return path

        # 如果都找不到，返回默认值并打印警告
        print("⚠️ 未找到FFmpeg，使用默认路径")
        print("💡 请确保FFmpeg已安装并添加到PATH，或安装到 C:/ffmpeg/")
        return 'ffmpeg'  # 默认假设在PATH中

    def get_streamer(self, resolution='640x640', fps=30):
        """
        获取指定配置的视频流处理器

        参数:
            resolution: 视频分辨率 (格式: '宽x高')
            fps: 帧率 (frames per second)

        返回:
            Streamer: 配置好的流处理器对象
        """
        # 生成配置键
        key = f"{resolution}_{fps}"

        # 如果已有相同配置的处理器，直接返回
        if key in self.processes:
            return self.processes[key]

        # 解析分辨率 (如: '640x480' -> (640, 480))
        width, height = map(int, resolution.split('x'))

        # 构建FFmpeg推流命令 - 低延时优化
        command = [
            self.ffmpeg_path,
            '-y',  # 覆盖输出文件而不询问
            '-f', 'rawvideo',  # 输入格式为原始视频
            '-vcodec', 'rawvideo',  # 原始视频编解码器
            '-pix_fmt', 'bgr24',  # OpenCV默认的像素格式
            '-s', f"{width}x{height}",  # 视频分辨率
            '-r', str(fps),  # 帧率
            '-i', '-',  # 从标准输入读取数据

            # 视频编码优化
            '-c:v', 'libx264',  # 使用H.264编码
            '-preset', 'ultrafast',  # 最快编码预设
            '-tune', 'zerolatency',  # 零延迟调优
            '-g', '30',  # GOP大小
            '-b:v', '1000k',  # 视频码率
            '-maxrate', '1000k',  # 最大码率
            '-bufsize', '500k',  # 缓冲区大小

            # 输出格式
            '-pix_fmt', 'yuv420p',  # 输出像素格式
            '-f', 'flv',  # 输出格式为FLV

            self.rtmp_url  # RTMP服务器地址
        ]

        # 启动FFmpeg子进程
        process = subprocess.Popen(
            command,
            stdin=subprocess.PIPE,  # 标准输入管道(用于发送视频帧)
            stdout=subprocess.PIPE,  # 标准输出管道
            stderr=subprocess.PIPE  # 标准错误管道
        )

        # 创建流处理器对象
        streamer = Streamer(process, width, height)

        # 缓存处理器
        self.processes[key] = streamer

        return streamer

    def release_all(self):
        """
        释放所有流处理器资源
        """
        for streamer in self.processes.values():
            streamer.release()
        self.processes.clear()  # 清空处理器字典


class Streamer:
    """
    视频流处理器，负责处理视频帧并推送到RTMP服务器

    属性:
        process: FFmpeg子进程对象
        width: 视频宽度
        height: 视频高度
    """

    def __init__(self, process, width, height):
        """
        初始化流处理器

        参数:
            process: FFmpeg子进程
            width: 视频宽度
            height: 视频高度
        """
        self.process = process
        self.width = width
        self.height = height

    def process_frame(self, frame):
        """
        处理视频帧并推送到流

        参数:
            frame: OpenCV视频帧 (numpy数组)
        """
        # 调整帧大小以匹配流分辨率
        if frame.shape[0] != self.height or frame.shape[1] != self.width:
            frame = cv2.resize(frame, (self.width, self.height))

        # 将帧数据写入FFmpeg的标准输入
        # tobytes()将numpy数组转换为字节序列
        self.process.stdin.write(frame.tobytes())

    def release(self):
        """
        释放流处理器资源
        """
        try:
            # 关闭标准输入管道
            self.process.stdin.close()
            # 终止进程
            self.process.terminate()
            # 等待进程结束(最多5秒)
            self.process.wait(timeout=5)
        except Exception as e:
            # 忽略任何异常
            pass
