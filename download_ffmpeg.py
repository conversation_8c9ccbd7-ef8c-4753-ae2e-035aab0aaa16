#!/usr/bin/env python3
"""
FFmpeg 自动下载和安装脚本
适用于Windows 10系统
"""

import os
import sys
import zipfile
import requests
import shutil
from pathlib import Path
import subprocess

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"📥 {title}")
    print("=" * 60)

def download_ffmpeg():
    """下载FFmpeg"""
    print_header("下载FFmpeg")
    
    # FFmpeg下载配置
    download_url = "https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip"
    temp_file = "ffmpeg-release-essentials.zip"
    install_dir = Path("C:/ffmpeg")
    
    print(f"📍 下载地址: {download_url}")
    print(f"📁 安装目录: {install_dir}")
    print(f"📦 临时文件: {temp_file}")
    print()
    
    try:
        # 检查是否已存在
        if install_dir.exists() and (install_dir / "bin" / "ffmpeg.exe").exists():
            print("⚠️ FFmpeg 似乎已经安装")
            choice = input("是否重新下载安装? (y/N): ").lower().strip()
            if choice != 'y':
                return str(install_dir / "bin" / "ffmpeg.exe")
        
        # 创建安装目录
        install_dir.mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建安装目录: {install_dir}")
        
        # 下载文件
        print("📥 开始下载FFmpeg (这可能需要几分钟)...")
        print("💡 请耐心等待，文件大小约50MB")
        
        response = requests.get(download_url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(temp_file, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        print(f"\r📊 下载进度: {percent:.1f}% ({downloaded}/{total_size} bytes)", end='')
        
        print("\n✅ 下载完成")
        
        # 解压文件
        print("📦 解压FFmpeg...")
        with zipfile.ZipFile(temp_file, 'r') as zip_ref:
            zip_ref.extractall(install_dir)
        
        # 查找解压后的目录并移动文件
        for item in install_dir.iterdir():
            if item.is_dir() and item.name.startswith('ffmpeg-'):
                print(f"📁 找到FFmpeg目录: {item}")
                
                # 移动所有文件到安装目录
                for sub_item in item.iterdir():
                    dest = install_dir / sub_item.name
                    if dest.exists():
                        if dest.is_dir():
                            shutil.rmtree(dest)
                        else:
                            dest.unlink()
                    shutil.move(str(sub_item), str(dest))
                
                # 删除空目录
                item.rmdir()
                break
        
        # 清理临时文件
        if os.path.exists(temp_file):
            os.remove(temp_file)
            print("🗑️ 清理临时文件")
        
        # 验证安装
        ffmpeg_exe = install_dir / "bin" / "ffmpeg.exe"
        if ffmpeg_exe.exists():
            print(f"✅ FFmpeg 安装成功: {ffmpeg_exe}")
            return str(ffmpeg_exe)
        else:
            print("❌ 安装失败，未找到 ffmpeg.exe")
            return None
            
    except requests.RequestException as e:
        print(f"❌ 下载失败: {e}")
        print("💡 请检查网络连接或手动下载")
        return None
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return None

def add_to_path(ffmpeg_path):
    """添加FFmpeg到系统PATH"""
    print_header("配置系统PATH")
    
    ffmpeg_dir = str(Path(ffmpeg_path).parent)
    print(f"📁 FFmpeg目录: {ffmpeg_dir}")
    
    try:
        # 检查是否已在PATH中
        current_path = os.environ.get('PATH', '')
        if ffmpeg_dir in current_path:
            print("✅ FFmpeg 已在PATH中")
            return True
        
        # Windows系统添加到PATH
        if sys.platform == 'win32':
            print("🔧 添加到Windows系统PATH...")
            
            # 使用setx命令添加到用户PATH
            cmd = f'setx PATH "%PATH%;{ffmpeg_dir}"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 成功添加到用户PATH")
                print("💡 重启命令提示符后生效")
                return True
            else:
                print(f"⚠️ 添加到PATH失败: {result.stderr}")
                print("💡 请手动添加到系统环境变量")
                return False
        else:
            print("💡 非Windows系统，请手动添加到PATH")
            return False
            
    except Exception as e:
        print(f"❌ 配置PATH出错: {e}")
        return False

def test_installation(ffmpeg_path):
    """测试FFmpeg安装"""
    print_header("测试FFmpeg安装")
    
    try:
        # 测试版本命令
        result = subprocess.run(
            [ffmpeg_path, '-version'], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ 版本信息: {version_line}")
            
            # 测试RTMP支持
            protocols_result = subprocess.run(
                [ffmpeg_path, '-protocols'], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if 'rtmp' in protocols_result.stdout:
                print("✅ RTMP 协议支持")
            else:
                print("⚠️ RTMP 协议不支持")
            
            return True
        else:
            print(f"❌ FFmpeg 测试失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False

def main():
    """主函数"""
    print("📥 FFmpeg 自动下载安装工具")
    print("=" * 60)
    print("💡 此工具将下载并安装FFmpeg到 C:/ffmpeg/")
    print("💡 需要管理员权限来修改系统PATH")
    print()
    
    # 下载和安装FFmpeg
    ffmpeg_path = download_ffmpeg()
    
    if ffmpeg_path:
        # 添加到PATH
        path_success = add_to_path(ffmpeg_path)
        
        # 测试安装
        test_success = test_installation(ffmpeg_path)
        
        # 总结
        print_header("安装总结")
        print(f"📍 安装位置: {ffmpeg_path}")
        print(f"🌐 PATH配置: {'✅ 成功' if path_success else '⚠️ 需要手动配置'}")
        print(f"🧪 功能测试: {'✅ 通过' if test_success else '❌ 失败'}")
        
        if test_success:
            print("\n🎉 FFmpeg 安装完成！")
            print("💡 现在可以运行 YOLOv8 检测系统了")
        else:
            print("\n⚠️ 安装可能存在问题")
            print("💡 请运行 verify_ffmpeg.py 进行详细检查")
    else:
        print("\n❌ FFmpeg 安装失败")
        print("💡 请尝试手动安装或运行 install_ffmpeg.bat")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
