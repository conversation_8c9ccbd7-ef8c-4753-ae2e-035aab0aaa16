@echo off
chcp 65001 >nul
echo ========================================
echo 🚀 YOLOv8 检测系统启动脚本
echo ========================================
echo.

:: 检查Python
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Python 未安装或不在PATH中
    echo 💡 请先安装Python 3.7+
    pause
    exit /b 1
)

echo ✅ Python 已安装

:: 检查FFmpeg
ffmpeg -version >nul 2>&1
if %errorLevel% neq 0 (
    echo ⚠️ FFmpeg 未找到
    echo 💡 正在检查常见安装位置...
    
    if exist "C:\ffmpeg\bin\ffmpeg.exe" (
        echo ✅ 找到FFmpeg: C:\ffmpeg\bin\ffmpeg.exe
        set PATH=%PATH%;C:\ffmpeg\bin
    ) else if exist "C:\ffmpeg\ffmpeg.exe" (
        echo ✅ 找到FFmpeg: C:\ffmpeg\ffmpeg.exe
        set PATH=%PATH%;C:\ffmpeg
    ) else (
        echo ❌ 未找到FFmpeg
        echo 💡 请运行以下命令之一:
        echo    1. install_ffmpeg.bat (自动安装)
        echo    2. python download_ffmpeg.py (Python安装)
        echo    3. python verify_ffmpeg.py (检查状态)
        choice /C YN /M "是否现在安装FFmpeg? (Y/N)"
        if errorlevel 2 goto :skip_ffmpeg
        
        echo 🔧 启动FFmpeg安装...
        call install_ffmpeg.bat
        if %errorLevel% neq 0 (
            echo ❌ FFmpeg安装失败
            goto :skip_ffmpeg
        )
    )
) else (
    echo ✅ FFmpeg 已安装
)

:skip_ffmpeg

:: 检查依赖包
echo.
echo 🔍 检查Python依赖包...
python -c "import cv2, ultralytics, flask" >nul 2>&1
if %errorLevel% neq 0 (
    echo ⚠️ 缺少必要的Python包
    echo 💡 正在安装依赖包...
    pip install -r requirements.txt
    if %errorLevel% neq 0 (
        echo ❌ 依赖包安装失败
        echo 💡 请手动运行: pip install -r requirements.txt
        pause
        exit /b 1
    )
)

echo ✅ Python依赖包检查完成

:: 检查模型文件
echo.
echo 🔍 检查AI模型文件...
if not exist "server\models\best.pt" (
    echo ❌ 模型文件不存在: server\models\best.pt
    echo 💡 请确保YOLOv8模型文件存在
    pause
    exit /b 1
)

echo ✅ 模型文件存在

:: 启动RTMP服务器
echo.
echo 🎬 启动RTMP服务器...
cd nginx-rtmp
start /B nginx.exe
cd ..

:: 等待RTMP服务器启动
timeout /t 3 /nobreak >nul

:: 检查RTMP服务器
netstat -an | findstr ":1935" >nul
if %errorLevel% == 0 (
    echo ✅ RTMP服务器已启动 (端口1935)
) else (
    echo ⚠️ RTMP服务器可能未正常启动
    echo 💡 请检查nginx-rtmp目录中的nginx.exe
)

:: 启动后端服务器
echo.
echo 🖥️ 启动后端服务器...
echo 💡 服务器将在 http://localhost:5000 运行
echo 💡 按 Ctrl+C 停止服务器
echo.

start /B python simple_start.py

:: 等待服务器启动
echo 🔄 等待服务器启动...
timeout /t 5 /nobreak >nul

:: 检查服务器状态
curl -s http://localhost:5000/api/status >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 后端服务器已启动
) else (
    echo ⚠️ 后端服务器可能未正常启动
    echo 💡 请检查服务器日志
)

:: 启动客户端
echo.
echo 🖼️ 启动客户端GUI...
echo 💡 客户端窗口即将打开
echo.

python client\main.py

:: 清理
echo.
echo 🛑 正在停止服务...

:: 停止nginx
cd nginx-rtmp
nginx.exe -s stop >nul 2>&1
cd ..

echo ✅ 服务已停止
pause
