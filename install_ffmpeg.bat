@echo off
chcp 65001 >nul
echo ========================================
echo 🔧 FFmpeg 自动安装脚本 (Windows 10)
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 管理员权限确认
) else (
    echo ❌ 需要管理员权限运行此脚本
    echo 💡 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

:: 设置变量
set FFMPEG_DIR=C:\ffmpeg
set FFMPEG_BIN=%FFMPEG_DIR%\bin
set DOWNLOAD_URL=https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip
set TEMP_FILE=%TEMP%\ffmpeg-release-essentials.zip

echo.
echo 📁 安装目录: %FFMPEG_DIR%
echo 📥 下载地址: %DOWNLOAD_URL%
echo.

:: 检查是否已安装
if exist "%FFMPEG_BIN%\ffmpeg.exe" (
    echo ⚠️ FFmpeg 似乎已经安装
    echo 📍 位置: %FFMPEG_BIN%\ffmpeg.exe
    choice /C YN /M "是否重新安装? (Y/N)"
    if errorlevel 2 goto :check_path
)

:: 创建安装目录
echo 📁 创建安装目录...
if not exist "%FFMPEG_DIR%" mkdir "%FFMPEG_DIR%"

:: 下载FFmpeg
echo.
echo 📥 下载FFmpeg (这可能需要几分钟)...
echo 💡 如果下载失败，请手动下载并解压到 %FFMPEG_DIR%
echo.

:: 使用PowerShell下载
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri '%DOWNLOAD_URL%' -OutFile '%TEMP_FILE%' -UseBasicParsing}"

if not exist "%TEMP_FILE%" (
    echo ❌ 下载失败
    echo 💡 请手动下载FFmpeg:
    echo    1. 访问: https://www.gyan.dev/ffmpeg/builds/
    echo    2. 下载 "release essentials" 版本
    echo    3. 解压到 %FFMPEG_DIR%
    pause
    exit /b 1
)

echo ✅ 下载完成

:: 解压文件
echo.
echo 📦 解压FFmpeg...
powershell -Command "Expand-Archive -Path '%TEMP_FILE%' -DestinationPath '%FFMPEG_DIR%' -Force"

:: 查找解压后的目录并移动文件
for /d %%i in ("%FFMPEG_DIR%\ffmpeg-*") do (
    echo 📁 找到FFmpeg目录: %%i
    xcopy "%%i\*" "%FFMPEG_DIR%\" /E /H /Y
    rmdir "%%i" /S /Q
)

:: 清理临时文件
del "%TEMP_FILE%" >nul 2>&1

:: 验证安装
if exist "%FFMPEG_BIN%\ffmpeg.exe" (
    echo ✅ FFmpeg 安装成功
    echo 📍 位置: %FFMPEG_BIN%\ffmpeg.exe
) else (
    echo ❌ 安装失败，未找到 ffmpeg.exe
    pause
    exit /b 1
)

:check_path
:: 检查PATH环境变量
echo.
echo 🔍 检查PATH环境变量...

:: 获取当前PATH
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set SYSTEM_PATH=%%b
for /f "tokens=2*" %%a in ('reg query "HKCU\Environment" /v PATH 2^>nul') do set USER_PATH=%%b

:: 检查是否已在PATH中
echo %SYSTEM_PATH% | findstr /C:"%FFMPEG_BIN%" >nul
if %errorlevel% == 0 (
    echo ✅ FFmpeg 已在系统PATH中
    goto :test_ffmpeg
)

echo %USER_PATH% | findstr /C:"%FFMPEG_BIN%" >nul
if %errorlevel% == 0 (
    echo ✅ FFmpeg 已在用户PATH中
    goto :test_ffmpeg
)

:: 添加到系统PATH
echo 📝 添加FFmpeg到系统PATH...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH /t REG_EXPAND_SZ /d "%SYSTEM_PATH%;%FFMPEG_BIN%" /f >nul

if %errorlevel% == 0 (
    echo ✅ 成功添加到系统PATH
    echo 💡 重启命令提示符后生效
) else (
    echo ⚠️ 添加到系统PATH失败，尝试添加到用户PATH
    reg add "HKCU\Environment" /v PATH /t REG_EXPAND_SZ /d "%USER_PATH%;%FFMPEG_BIN%" /f >nul
    if %errorlevel% == 0 (
        echo ✅ 成功添加到用户PATH
    ) else (
        echo ❌ 添加到PATH失败
    )
)

:test_ffmpeg
:: 测试FFmpeg
echo.
echo 🧪 测试FFmpeg安装...
"%FFMPEG_BIN%\ffmpeg.exe" -version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ FFmpeg 测试成功
    echo.
    echo 🎉 安装完成！
    echo 💡 现在可以运行 YOLOv8 检测系统了
) else (
    echo ❌ FFmpeg 测试失败
    echo 💡 请检查安装是否正确
)

echo.
echo 📋 安装摘要:
echo    📍 安装位置: %FFMPEG_DIR%
echo    🔧 可执行文件: %FFMPEG_BIN%\ffmpeg.exe
echo    🌐 PATH状态: 已添加
echo.
pause
