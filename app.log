2025-06-11 22:53:48,304 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-11 22:53:48,304 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-11 22:53:48,304 - INFO - Model: E:\yolov8_count\server\models\best.pt
2025-06-11 22:53:48,312 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-11 22:53:48,312 - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 22:54:06,324 - INFO - Received status request
2025-06-11 22:54:06,324 - INFO - Current status: stopped, counts: {}
2025-06-11 22:54:06,324 - INFO - 127.0.0.1 - - [11/Jun/2025 22:54:06] "GET /api/status HTTP/1.1" 200 -
2025-06-11 22:54:07,369 - INFO - Received status request
2025-06-11 22:54:07,369 - INFO - Current status: stopped, counts: {}
2025-06-11 22:54:07,369 - INFO - 127.0.0.1 - - [11/Jun/2025 22:54:07] "GET /api/status HTTP/1.1" 200 -
2025-06-11 22:54:09,353 - INFO - Received status request
2025-06-11 22:54:09,353 - INFO - Current status: stopped, counts: {}
2025-06-11 22:54:09,353 - INFO - 127.0.0.1 - - [11/Jun/2025 22:54:09] "GET /api/status HTTP/1.1" 200 -
2025-06-11 22:54:11,963 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x640', 'fps': 30}
2025-06-11 22:54:11,963 - INFO - Starting stream with params - camera: 0, resolution: 640x640, fps: 30
2025-06-11 22:54:11,963 - INFO - StreamThread-20250611225411 - Starting stream processing
2025-06-11 22:54:11,963 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-11 22:54:11,963 - INFO - 127.0.0.1 - - [11/Jun/2025 22:54:11] "POST /api/start HTTP/1.1" 200 -
2025-06-11 22:54:13,989 - INFO - Received status request
2025-06-11 22:54:13,989 - INFO - Current status: running, counts: {}
2025-06-11 22:54:13,989 - INFO - 127.0.0.1 - - [11/Jun/2025 22:54:13] "GET /api/status HTTP/1.1" 200 -
2025-06-11 22:54:15,523 - INFO - StreamThread-20250611225411 - Camera initialized
2025-06-11 23:06:35,413 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-11 23:06:35,413 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-11 23:06:35,413 - INFO - Model: E:\yolov8_count\server\models\best.pt
2025-06-11 23:06:35,423 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-11 23:06:35,423 - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 23:07:17,456 - INFO - Received status request
2025-06-11 23:07:17,456 - INFO - Current status: stopped, counts: {}
2025-06-11 23:07:17,456 - INFO - 127.0.0.1 - - [11/Jun/2025 23:07:17] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:07:45,293 - INFO - Received status request
2025-06-11 23:07:45,293 - INFO - Current status: stopped, counts: {}
2025-06-11 23:07:45,293 - INFO - 127.0.0.1 - - [11/Jun/2025 23:07:45] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:07:47,349 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x640', 'fps': 30}
2025-06-11 23:07:47,349 - INFO - Starting stream with params - camera: 0, resolution: 640x640, fps: 30
2025-06-11 23:07:47,349 - INFO - StreamThread-20250611230747 - Starting stream processing
2025-06-11 23:07:47,349 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-11 23:07:47,349 - INFO - 127.0.0.1 - - [11/Jun/2025 23:07:47] "POST /api/start HTTP/1.1" 200 -
2025-06-11 23:07:51,622 - INFO - StreamThread-20250611230747 - Camera initialized
2025-06-11 23:07:51,654 - INFO - StreamThread-20250611230747 - Streamer initialized
2025-06-11 23:07:54,404 - INFO - Received status request
2025-06-11 23:07:54,405 - INFO - Current status: running, counts: {'C': 10, 'LED': 4}
2025-06-11 23:07:54,406 - INFO - 127.0.0.1 - - [11/Jun/2025 23:07:54] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:07:56,447 - INFO - Received stop request
2025-06-11 23:07:56,448 - INFO - Stopping stream...
2025-06-11 23:07:57,180 - INFO - StreamThread-20250611230747 - Resources released, processed 29 frames in total
2025-06-11 23:07:57,180 - INFO - Stream thread joined successfully
2025-06-11 23:07:57,180 - INFO - Stream stopped successfully
2025-06-11 23:07:57,180 - INFO - 127.0.0.1 - - [11/Jun/2025 23:07:57] "GET /api/stop HTTP/1.1" 200 -
2025-06-11 23:09:48,806 - INFO - Received status request
2025-06-11 23:09:48,806 - INFO - Current status: stopped, counts: {'C': 7, 'LED': 1}
2025-06-11 23:09:48,806 - INFO - 127.0.0.1 - - [11/Jun/2025 23:09:48] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:09:50,822 - INFO - Received status request
2025-06-11 23:09:50,822 - INFO - Current status: stopped, counts: {'C': 7, 'LED': 1}
2025-06-11 23:09:50,822 - INFO - 127.0.0.1 - - [11/Jun/2025 23:09:50] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:09:52,838 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x640', 'fps': 30}
2025-06-11 23:09:52,838 - INFO - Starting stream with params - camera: 0, resolution: 640x640, fps: 30
2025-06-11 23:09:52,838 - INFO - StreamThread-20250611230952 - Starting stream processing
2025-06-11 23:09:52,838 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-11 23:09:52,838 - INFO - StreamThread-20250611230952 - Camera initialized
2025-06-11 23:09:52,838 - INFO - 127.0.0.1 - - [11/Jun/2025 23:09:52] "POST /api/start HTTP/1.1" 200 -
2025-06-11 23:09:52,838 - INFO - StreamThread-20250611230952 - Streamer initialized
2025-06-11 23:09:52,838 - ERROR - StreamThread-20250611230952 - Failed to read frame from camera
2025-06-11 23:09:52,838 - INFO - StreamThread-20250611230952 - Resources released, processed 0 frames in total
2025-06-11 23:09:54,870 - INFO - Received status request
2025-06-11 23:09:54,871 - INFO - Current status: running, counts: {'C': 7, 'LED': 1}
2025-06-11 23:09:54,872 - INFO - 127.0.0.1 - - [11/Jun/2025 23:09:54] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:12:03,442 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-11 23:12:03,442 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-11 23:12:03,442 - INFO - Model: e:\yolov8_count\server\models\best.pt
2025-06-11 23:12:03,448 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-11 23:12:03,449 - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 23:12:14,478 - INFO - Received status request
2025-06-11 23:12:14,478 - INFO - Current status: stopped, counts: {}
2025-06-11 23:12:14,478 - INFO - 127.0.0.1 - - [11/Jun/2025 23:12:14] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:12:16,525 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x640', 'fps': 30}
2025-06-11 23:12:16,525 - INFO - Starting stream with params - camera: 0, resolution: 640x640, fps: 30
2025-06-11 23:12:16,525 - INFO - StreamThread-20250611231216 - Starting stream processing
2025-06-11 23:12:16,525 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-11 23:12:16,525 - INFO - 127.0.0.1 - - [11/Jun/2025 23:12:16] "POST /api/start HTTP/1.1" 200 -
2025-06-11 23:12:18,550 - INFO - Received status request
2025-06-11 23:12:18,551 - INFO - Current status: running, counts: {}
2025-06-11 23:12:18,551 - INFO - 127.0.0.1 - - [11/Jun/2025 23:12:18] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:12:20,167 - INFO - StreamThread-20250611231216 - Camera initialized
2025-06-11 23:12:20,195 - INFO - StreamThread-20250611231216 - Streamer initialized
2025-06-11 23:12:33,800 - INFO - StreamThread-20250611231216 - Processed 100 frames, FPS: 7.35
2025-06-11 23:12:44,087 - INFO - Received status request
2025-06-11 23:12:44,088 - INFO - Current status: running, counts: {'C': 3, 'LED': 2}
2025-06-11 23:12:44,089 - INFO - 127.0.0.1 - - [11/Jun/2025 23:12:44] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:12:47,128 - INFO - StreamThread-20250611231216 - Processed 200 frames, FPS: 7.50
2025-06-11 23:12:47,509 - INFO - Received status request
2025-06-11 23:12:47,510 - INFO - Current status: running, counts: {'C': 3, 'LED': 5}
2025-06-11 23:12:47,511 - INFO - 127.0.0.1 - - [11/Jun/2025 23:12:47] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:12:50,944 - INFO - Received status request
2025-06-11 23:12:50,945 - INFO - Current status: running, counts: {'C': 4, 'LED': 4}
2025-06-11 23:12:50,946 - INFO - 127.0.0.1 - - [11/Jun/2025 23:12:50] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:12:54,366 - INFO - Received status request
2025-06-11 23:12:54,367 - INFO - Current status: running, counts: {'C': 4, 'LED': 5}
2025-06-11 23:12:54,368 - INFO - 127.0.0.1 - - [11/Jun/2025 23:12:54] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:12:57,789 - INFO - Received status request
2025-06-11 23:12:57,790 - INFO - Current status: running, counts: {'C': 5, 'LED': 4}
2025-06-11 23:12:57,791 - INFO - 127.0.0.1 - - [11/Jun/2025 23:12:57] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:13:00,456 - INFO - StreamThread-20250611231216 - Processed 300 frames, FPS: 7.50
2025-06-11 23:13:01,205 - INFO - Received status request
2025-06-11 23:13:01,206 - INFO - Current status: running, counts: {'C': 5, 'LED': 4}
2025-06-11 23:13:01,206 - INFO - 127.0.0.1 - - [11/Jun/2025 23:13:01] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:13:04,636 - INFO - Received status request
2025-06-11 23:13:04,637 - INFO - Current status: running, counts: {'C': 4, 'LED': 5}
2025-06-11 23:13:04,637 - INFO - 127.0.0.1 - - [11/Jun/2025 23:13:04] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:13:08,003 - INFO - Received status request
2025-06-11 23:13:08,004 - INFO - Current status: running, counts: {'C': 4, 'LED': 4}
2025-06-11 23:13:08,004 - INFO - 127.0.0.1 - - [11/Jun/2025 23:13:08] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:13:11,431 - INFO - Received status request
2025-06-11 23:13:11,432 - INFO - Current status: running, counts: {'C': 2, 'LED': 4}
2025-06-11 23:13:11,433 - INFO - 127.0.0.1 - - [11/Jun/2025 23:13:11] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:13:13,784 - INFO - StreamThread-20250611231216 - Processed 400 frames, FPS: 7.50
2025-06-11 23:13:14,844 - INFO - Received status request
2025-06-11 23:13:14,845 - INFO - Current status: running, counts: {'C': 2, 'LED': 1}
2025-06-11 23:13:14,845 - INFO - 127.0.0.1 - - [11/Jun/2025 23:13:14] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:13:18,267 - INFO - Received status request
2025-06-11 23:13:18,268 - INFO - Current status: running, counts: {'C': 2, 'LED': 1}
2025-06-11 23:13:18,268 - INFO - 127.0.0.1 - - [11/Jun/2025 23:13:18] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:13:21,689 - INFO - Received status request
2025-06-11 23:13:21,690 - INFO - Current status: running, counts: {'C': 2, 'LED': 1}
2025-06-11 23:13:21,690 - INFO - 127.0.0.1 - - [11/Jun/2025 23:13:21] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:13:25,171 - INFO - Received status request
2025-06-11 23:13:25,172 - INFO - Current status: running, counts: {'C': 1, 'LED': 1}
2025-06-11 23:13:25,172 - INFO - 127.0.0.1 - - [11/Jun/2025 23:13:25] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:13:27,096 - INFO - StreamThread-20250611231216 - Processed 500 frames, FPS: 7.51
2025-06-11 23:13:28,601 - INFO - Received status request
2025-06-11 23:13:28,601 - INFO - Current status: running, counts: {'C': 10, 'LED': 2}
2025-06-11 23:13:28,603 - INFO - 127.0.0.1 - - [11/Jun/2025 23:13:28] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:13:32,025 - INFO - Received status request
2025-06-11 23:13:32,026 - INFO - Current status: running, counts: {'C': 7, 'LED': 4}
2025-06-11 23:13:32,027 - INFO - 127.0.0.1 - - [11/Jun/2025 23:13:32] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:13:35,436 - INFO - Received status request
2025-06-11 23:13:35,437 - INFO - Current status: running, counts: {'C': 4, 'LED': 3}
2025-06-11 23:13:35,437 - INFO - 127.0.0.1 - - [11/Jun/2025 23:13:35] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:13:38,862 - INFO - Received status request
2025-06-11 23:13:38,863 - INFO - Current status: running, counts: {}
2025-06-11 23:13:38,863 - INFO - 127.0.0.1 - - [11/Jun/2025 23:13:38] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:13:40,424 - INFO - StreamThread-20250611231216 - Processed 600 frames, FPS: 7.50
2025-06-11 23:13:42,299 - INFO - Received status request
2025-06-11 23:13:42,300 - INFO - Current status: running, counts: {'C': 7, 'LED': 4}
2025-06-11 23:13:42,301 - INFO - 127.0.0.1 - - [11/Jun/2025 23:13:42] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:13:45,714 - INFO - Received status request
2025-06-11 23:13:45,715 - INFO - Current status: running, counts: {'C': 5, 'LED': 6}
2025-06-11 23:13:45,716 - INFO - 127.0.0.1 - - [11/Jun/2025 23:13:45] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:13:49,154 - INFO - Received status request
2025-06-11 23:13:49,154 - INFO - Current status: running, counts: {'C': 5, 'LED': 6}
2025-06-11 23:13:49,155 - INFO - 127.0.0.1 - - [11/Jun/2025 23:13:49] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:13:52,586 - INFO - Received status request
2025-06-11 23:13:52,587 - INFO - Current status: running, counts: {'C': 8, 'LED': 5}
2025-06-11 23:13:52,587 - INFO - 127.0.0.1 - - [11/Jun/2025 23:13:52] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:13:53,752 - INFO - StreamThread-20250611231216 - Processed 700 frames, FPS: 7.50
2025-06-11 23:13:55,992 - INFO - Received status request
2025-06-11 23:13:55,993 - INFO - Current status: running, counts: {'C': 4, 'LED': 4}
2025-06-11 23:13:55,994 - INFO - 127.0.0.1 - - [11/Jun/2025 23:13:55] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:13:59,411 - INFO - Received status request
2025-06-11 23:13:59,412 - INFO - Current status: running, counts: {'C': 5, 'LED': 6}
2025-06-11 23:13:59,412 - INFO - 127.0.0.1 - - [11/Jun/2025 23:13:59] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:14:01,338 - INFO - Received stop request
2025-06-11 23:14:01,339 - INFO - Stopping stream...
2025-06-11 23:14:02,080 - INFO - StreamThread-20250611231216 - Resources released, processed 757 frames in total
2025-06-11 23:14:02,081 - INFO - Stream thread joined successfully
2025-06-11 23:14:02,081 - INFO - Stream stopped successfully
2025-06-11 23:14:02,082 - INFO - 127.0.0.1 - - [11/Jun/2025 23:14:02] "GET /api/stop HTTP/1.1" 200 -
2025-06-11 23:14:02,829 - INFO - Received status request
2025-06-11 23:14:02,829 - INFO - Current status: stopped, counts: {'C': 5, 'LED': 3}
2025-06-11 23:14:02,829 - INFO - 127.0.0.1 - - [11/Jun/2025 23:14:02] "GET /api/status HTTP/1.1" 200 -
2025-06-11 23:14:03,896 - INFO - Received status request
2025-06-11 23:14:03,896 - INFO - Current status: stopped, counts: {'C': 5, 'LED': 3}
2025-06-11 23:14:03,896 - INFO - 127.0.0.1 - - [11/Jun/2025 23:14:03] "GET /api/status HTTP/1.1" 200 -
