2025-06-11 22:53:48,304 - INFO - Starting YOLOv8 detection system with low-latency optimization...
2025-06-11 22:53:48,304 - INFO - RTMP URL: rtmp://localhost/live/stream
2025-06-11 22:53:48,304 - INFO - Model: E:\yolov8_count\server\models\best.pt
2025-06-11 22:53:48,312 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-11 22:53:48,312 - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 22:54:06,324 - INFO - Received status request
2025-06-11 22:54:06,324 - INFO - Current status: stopped, counts: {}
2025-06-11 22:54:06,324 - INFO - 127.0.0.1 - - [11/Jun/2025 22:54:06] "GET /api/status HTTP/1.1" 200 -
2025-06-11 22:54:07,369 - INFO - Received status request
2025-06-11 22:54:07,369 - INFO - Current status: stopped, counts: {}
2025-06-11 22:54:07,369 - INFO - 127.0.0.1 - - [11/Jun/2025 22:54:07] "GET /api/status HTTP/1.1" 200 -
2025-06-11 22:54:09,353 - INFO - Received status request
2025-06-11 22:54:09,353 - INFO - Current status: stopped, counts: {}
2025-06-11 22:54:09,353 - INFO - 127.0.0.1 - - [11/Jun/2025 22:54:09] "GET /api/status HTTP/1.1" 200 -
2025-06-11 22:54:11,963 - INFO - Received start request with data: {'camera_index': 0, 'resolution': '640x640', 'fps': 30}
2025-06-11 22:54:11,963 - INFO - Starting stream with params - camera: 0, resolution: 640x640, fps: 30
2025-06-11 22:54:11,963 - INFO - StreamThread-20250611225411 - Starting stream processing
2025-06-11 22:54:11,963 - INFO - Stream started successfully, RTMP URL: rtmp://localhost/live/stream
2025-06-11 22:54:11,963 - INFO - 127.0.0.1 - - [11/Jun/2025 22:54:11] "POST /api/start HTTP/1.1" 200 -
2025-06-11 22:54:13,989 - INFO - Received status request
2025-06-11 22:54:13,989 - INFO - Current status: running, counts: {}
2025-06-11 22:54:13,989 - INFO - 127.0.0.1 - - [11/Jun/2025 22:54:13] "GET /api/status HTTP/1.1" 200 -
2025-06-11 22:54:15,523 - INFO - StreamThread-20250611225411 - Camera initialized
