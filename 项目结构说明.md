# 🗂️ YOLOv8计数系统 - 项目结构说明

## 📁 核心目录结构

```
yolov8_count/
├── 📁 client/                    # 客户端GUI应用
│   ├── main.py                   # 主GUI程序
│   └── video_stream_improved.py  # 改进的视频流处理
│
├── 📁 server/                    # 服务器端应用
│   ├── app.py                    # Flask API服务器
│   ├── config.py                 # 配置文件
│   ├── 📁 models/                # AI模型文件
│   │   └── best.pt               # YOLOv8训练模型
│   └── 📁 utils/                 # 工具模块
│       ├── camera.py             # 摄像头管理
│       ├── detection.py          # 检测算法
│       └── stream.py             # 视频流处理
│
├── 📁 nginx-rtmp/                # RTMP流媒体服务器
│   ├── nginx.exe                 # Nginx可执行文件
│   ├── 📁 conf/                  # 配置文件
│   └── stop.bat                  # 停止脚本
│
├── 🚀 simple_start.py            # 完整版启动脚本(需FFmpeg)
├── 🚀 simple_start_no_ffmpeg.py  # 简化版启动脚本(无FFmpeg)
├── 📋 requirements.txt           # Python依赖包列表
├── 📖 简单使用说明.md            # 使用说明文档
├── 📄 计算机视觉综合设计实验指导书.pdf  # 项目指导文档
│
├── 🔧 download_ffmpeg.py         # FFmpeg自动下载脚本
├── 🔧 install_ffmpeg.bat         # FFmpeg安装脚本
├── 🔧 verify_ffmpeg.py           # FFmpeg验证脚本
├── 🔧 setup_complete_system.py   # 完整系统测试脚本
├── 🔧 start.bat                  # Windows启动脚本
│
└── 📝 .gitignore                 # Git忽略文件配置
```

## 🗑️ 已清理的文件

### 删除的重复文件
- ❌ `nginx-rtmp-win32-master/` - 重复的nginx目录
- ❌ `server/nginx-rtmp/` - 重复的nginx配置

### 删除的缓存文件
- ❌ `__pycache__/` - Python缓存目录
- ❌ `*.log` - 日志文件

### 删除的弃用文件
- ❌ `simple_test.py` - 旧版测试脚本
- ❌ `test.bat` - 旧版测试批处理
- ❌ `start_system_demo.py` - 临时演示脚本

### 删除的空目录
- ❌ `server/static/` - 空的静态文件目录
- ❌ `server/templates/` - 空的模板目录

## 📋 文件功能说明

### 🎯 核心应用文件
| 文件 | 功能 | 必要性 |
|------|------|--------|
| `client/main.py` | 主GUI界面 | ⭐⭐⭐ 必需 |
| `server/app.py` | API服务器 | ⭐⭐⭐ 必需 |
| `server/models/best.pt` | AI模型 | ⭐⭐⭐ 必需 |

### 🚀 启动脚本
| 文件 | 功能 | 使用场景 |
|------|------|---------|
| `simple_start.py` | 完整版启动 | 已安装FFmpeg |
| `simple_start_no_ffmpeg.py` | 简化版启动 | 未安装FFmpeg |
| `start.bat` | Windows批处理启动 | Windows用户 |

### 🔧 工具脚本
| 文件 | 功能 | 使用时机 |
|------|------|---------|
| `download_ffmpeg.py` | 自动下载FFmpeg | 首次安装 |
| `verify_ffmpeg.py` | 验证FFmpeg安装 | 安装后验证 |
| `setup_complete_system.py` | 系统完整测试 | 部署验证 |

### 📖 文档文件
| 文件 | 功能 | 重要性 |
|------|------|--------|
| `简单使用说明.md` | 使用指南 | ⭐⭐ 重要 |
| `requirements.txt` | 依赖列表 | ⭐⭐⭐ 必需 |
| `项目结构说明.md` | 本文档 | ⭐ 参考 |

## 🎯 使用建议

### 🚀 快速启动
1. **首次使用**: 运行 `python setup_complete_system.py` 进行系统检查
2. **日常使用**: 运行 `python simple_start_no_ffmpeg.py` 启动服务器
3. **客户端**: 运行 `python client/main.py` 启动GUI

### 🔧 维护建议
1. **定期清理**: 删除生成的日志和缓存文件
2. **备份模型**: 定期备份 `server/models/best.pt` 文件
3. **更新依赖**: 定期更新 `requirements.txt` 中的包版本

## 📊 项目统计

- **总文件数**: 约20个核心文件
- **代码行数**: 约2000行Python代码
- **项目大小**: 约50MB (包含模型文件)
- **依赖包数**: 8个主要Python包

---
*最后更新: 项目清理完成后*
